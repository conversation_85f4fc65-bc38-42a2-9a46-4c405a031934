import { Schema } from "effect";

// Base ProductionFlow model
export const ProductionFlow = Schema.Struct({
	id: Schema.String,
	code: Schema.String,
	name: Schema.String,
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type ProductionFlow = typeof ProductionFlow.Type;

// Create ProductionFlow model
export const CreateProductionFlow = Schema.Struct({
	code: Schema.String,
	name: Schema.String,
});
export type CreateProductionFlow = typeof CreateProductionFlow.Type;

// Update ProductionFlow model
export const UpdateProductionFlow = Schema.Struct({
	id: Schema.String,
	code: Schema.String,
	name: Schema.String,
});
export type UpdateProductionFlow = typeof UpdateProductionFlow.Type;

// Activity model
export const Activity = Schema.Struct({
	id: Schema.String,
	productionFlowId: Schema.String,
	workAreaId: Schema.String,
	operationId: Schema.String,
	indexNumber: Schema.Number,
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Activity = typeof Activity.Type;

// Create Activity model
export const CreateActivity = Schema.Struct({
	productionFlowId: Schema.String,
	workAreaId: Schema.String,
	operationId: Schema.String,
	indexNumber: Schema.Number,
});
export type CreateActivity = typeof CreateActivity.Type;

// Activity Create Request model (for API)
export const ActivityCreateRequest = Schema.Struct({
	workAreaId: Schema.String,
	operationId: Schema.String,
	indexNumber: Schema.Number,
});
export type ActivityCreateRequest = typeof ActivityCreateRequest.Type;

// ProductionFlow Create With Activities model
export const ProductionFlowCreateWithActivities = Schema.Struct({
	code: Schema.String,
	name: Schema.String,
	activities: Schema.Array(ActivityCreateRequest),
});
export type ProductionFlowCreateWithActivities =
	typeof ProductionFlowCreateWithActivities.Type;

// WorkArea Info model
export const WorkAreaInfo = Schema.Struct({
	id: Schema.String,
	code: Schema.String,
	name: Schema.String,
});
export type WorkAreaInfo = typeof WorkAreaInfo.Type;

// Operation Info model
export const OperationInfo = Schema.Struct({
	id: Schema.String,
	code: Schema.String,
	name: Schema.String,
});
export type OperationInfo = typeof OperationInfo.Type;

// Activity Detail model (Activity with related info)
export const ActivityDetail = Schema.Struct({
	activity: Activity,
	workArea: WorkAreaInfo,
	operation: OperationInfo,
});
export type ActivityDetail = typeof ActivityDetail.Type;

// ProductionFlow With Activities model
export const ProductionFlowWithActivities = Schema.Struct({
	productionFlow: ProductionFlow,
	activities: Schema.Array(Activity),
});
export type ProductionFlowWithActivities =
	typeof ProductionFlowWithActivities.Type;

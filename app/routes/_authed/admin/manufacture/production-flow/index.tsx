import { Link, createFileRoute } from "@tanstack/react-router";
import { Plus } from "lucide-react";
import ProductionFlowTable from "~/modules/production-flow/components/ProductionFlowTable";

export const Route = createFileRoute(
	"/_authed/admin/manufacture/production-flow/",
)({
	component: RouteComponent,
});

function RouteComponent() {
	return (
		<>
			<div className="container mx-auto">
				<div className="card bg-base-300">
					<div className="card-body">
						<div>
							<Link
								to="/admin/manufacture/production-flow/create"
								className="btn btn-primary"
							>
								<Plus size={16} />
								Crear nuevo flujo de producción
							</Link>
						</div>
					</div>
					<ProductionFlowTable />
				</div>
			</div>
		</>
	);
}
